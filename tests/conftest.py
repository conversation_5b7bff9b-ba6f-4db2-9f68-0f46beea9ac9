"""
pytest配置文件
"""
import os
import sys
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, root_dir)
logger.info(f"项目根目录: {root_dir}")

# 确保使用config_fake.yaml作为配置文件
os.environ['CONFIG_FILE'] = 'config.yaml'
logger.info(f"使用配置文件: {os.environ['CONFIG_FILE']}")

@pytest.fixture
def mock_config():
    """模拟配置对象"""
    logger.info("创建模拟配置对象")
    config_mock = MagicMock()
    # 设置基本属性
    config_mock.EVENT_REPORT_URL = "http://example.com/api/event"
    config_mock.VIDEO_SAVE_URL = "http://example.com/api/video"
    config_mock.EVENT_REPORT_CAMERA_TYPE = "test_camera"
    config_mock.EVENT_REPORT_IDENTIFY_TYPE = 1
    config_mock.REAL_ESRGAN_PATH = "./lib/Real-ESRGAN"
    
    # 为ImageProcessor设置属性
    config_mock.PREPROCESS = True  # 确保这是一个布尔值而不是MagicMock
    
    logger.info("模拟配置对象创建完成")
    logger.info(f"模拟事件上报URL: {config_mock.EVENT_REPORT_URL}")
    logger.info(f"模拟视频保存URL: {config_mock.VIDEO_SAVE_URL}")
    return config_mock

@pytest.fixture
def mock_file_manager():
    """模拟文件管理器"""
    logger.info("创建模拟文件管理器")
    file_manager_mock = MagicMock()
    
    # 使用AsyncMock来模拟异步方法
    file_manager_mock.save_video = AsyncMock(return_value="/path/to/test_video.mp4")
    file_manager_mock.save_file = AsyncMock(return_value="/path/to/test_image.jpg")
    
    logger.info("模拟文件管理器创建完成")
    return file_manager_mock

@pytest.fixture
def sample_image():
    """创建一个简单的测试图像"""
    logger.info("创建测试图像")
    import numpy as np
    # 创建一个100x100的RGB图像
    image = np.zeros((100, 100, 3), dtype=np.uint8)
    logger.info(f"测试图像创建完成，形状: {image.shape}")
    return image 
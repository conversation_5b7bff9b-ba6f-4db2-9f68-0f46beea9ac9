import asyncio
import cv2
import logging
import threading
import time
from typing import Optional, List, Dict, Callable, Any
import numpy as np
from dataclasses import dataclass
from app.processor.image_processor import ImagePreprocessor
from app.utils.config import config
import uuid
from app.utils.file_manager import file_manager
from app.video.manager import VideoStreamManager
import os
import concurrent.futures
from app.database.websocket_client import WebSocketClient
from app.utils.frame_buffer import FrameBuffer, FrameInfo

logger = logging.getLogger(__name__)

class StreamProcessor:
    """视频流处理器，负责处理单个视频流的检测"""
    def __init__(self, drone_code: str, stream_id: str, stream_type: str = "drone", model_type: str = 'cv', 
                 on_detection: Optional[Callable[[Dict], None]] = None):
        """
        初始化流处理器
        :param drone_code: 无人机编号
        :param stream_id: 视频流ID
        :param stream_type: 视频流类型 ('drone' 或 'airport')
        :param model_type: 模型类型 ('cv' 或 'multimodal')
        :param on_detection: 检测到目标时的回调函数
        """
        self.drone_code = drone_code
        self.stream_id = stream_id
        self.stream_type = stream_type
        self.stream_key = f"{drone_code}_{stream_type}"
        self.logger = logger
        self.image_processor = ImagePreprocessor(model_type=model_type)
        self.frame_rate = config.video_stream.frame_rate  # 默认帧率
        self.is_running = False
        self.process_thread: Optional[threading.Thread] = None
        self.last_frame_time = 0
        self.loop = asyncio.get_event_loop()
        self.read_fail_threshold = config.video_stream.read_fail_threshold
        self.empty_frame_threshold = config.VIDEO_STREAM_EMPTY_FRAME_THRESHOLD
        self.detection_interval = config.video_stream.detection_interval
        self.on_detection = on_detection

        # 初始化帧缓存
        self.frame_buffer = FrameBuffer(config.video_stream.buffer_duration)
        # 添加视频计数器
        self.video_counter = 0
        # 视频流对象
        self.stream = None
        # 最后一次成功读取帧的时间
        self.last_successful_read = 0

        # 检测限流与熔断
        self._detection_semaphore = None  # 在事件循环内惰性初始化
        self._detection_failures = 0
        self._circuit_open_until = 0.0

    def get_frames_around_timestamp(self, timestamp: float, duration: float) -> List[FrameInfo]:
        """
        获取指定时间戳前后指定时长的帧
        :param timestamp: 目标时间戳（毫秒）
        :param duration: 前后时长（秒）
        :return: 帧列表
        """
        return self.frame_buffer.get_frames_around_timestamp(timestamp, duration)

    def get_frames_before_timestamp(self, timestamp: float, duration: float) -> List[FrameInfo]:
        """
        获取指定时间戳前面指定时长的帧
        :param timestamp: 目标时间戳（毫秒）
        :param duration: 前面的时长（秒）
        :return: 帧列表
        """
        return self.frame_buffer.get_frames_before_timestamp(timestamp, duration)

    async def start(self, stream) -> bool:
        """
        启动流处理
        :param stream: 视频流对象
        :return: 是否成功启动
        """
        if self.is_running:
            self.logger.warning(f"流处理器已经在运行: {self.stream_key}")
            return False
            
        if not stream:
            self.logger.error(f"无效的视频流对象: {self.stream_key}")
            return False
            
        # 不再检查流是否活跃，直接启动处理器
        self.stream = stream
        self.is_running = True
        self.process_thread = threading.Thread(
            target=self._process_stream,
            name=f"StreamProcessor-{self.stream_key}"
        )
        self.process_thread.daemon = True
        self.process_thread.start()
        self.logger.info(f"启动流处理器: {self.stream_key}")
        return True
        
    async def stop(self):
        """停止处理流"""
        if not self.is_running:
            return
            
        self.logger.info(f"停止流处理器: {self.stream_key}")
        self.is_running = False
        
        # 先停止视频流
        if self.stream:
            try:
                await self.stream.stop()
                # self.logger.info(f"已停止视频流: {self.stream_key}")
            except Exception as e:
                self.logger.error(f"停止视频流时出错: {self.stream_key}, 错误: {str(e)}")
        
        # 等待处理线程结束
        if self.process_thread:
            self.process_thread.join(timeout=5.0)
            if self.process_thread.is_alive():
                self.logger.warning(f"流处理线程未能正常结束: {self.stream_key}")
                
        self.stream = None
            
    def _process_stream(self):
        """处理流的主循环 - 重构为从流队列获取帧"""
        last_detection_time = 0
        read_fail_count = 0
        empty_frame_count = 0

        self.logger.info(f"开始处理流循环: {self.stream_key}")

        # 给流一些时间来初始化和稳定
        initialization_wait = 3.0  # 3秒初始化等待
        self.logger.info(f"等待流初始化: {initialization_wait}秒")
        time.sleep(initialization_wait)
        
        # 添加调试信息
        frame_count = 0
        debug_interval = 50  # 每50帧输出一次调试信息

        while self.is_running:
            try:
                # 从流获取帧（优先使用线程友好的阻塞接口，避免频繁跨事件循环）
                if hasattr(self.stream, 'get_frame_blocking'):
                    frame = self.stream.get_frame_blocking(timeout=config.FRAME_QUEUE_TIMEOUT)
                else:
                    frame_future = asyncio.run_coroutine_threadsafe(
                        self.stream.get_frame(), self.loop
                    )
                    try:
                        frame = frame_future.result(timeout=config.FRAME_QUEUE_TIMEOUT)
                    except concurrent.futures.TimeoutError:
                        frame = None

                frame_count += 1

                # 每50帧输出一次调试信息
                if frame_count % debug_interval == 0:
                    frame_status = "有效帧" if frame is not None else "空帧"
                    self.logger.debug(f"帧处理状态: {self.stream_key}, 第{frame_count}帧, 状态={frame_status}, 检测间隔={self.detection_interval}s")

                if frame is None:
                    empty_frame_count += 1

                    # 减少流状态检查频率，避免过度检查
                    if empty_frame_count % 100 == 0:
                        self.logger.debug(f"获取帧超时/空帧，检查流状态: {self.stream_key}, 次数: {empty_frame_count}")
                        try:
                            check_future = asyncio.run_coroutine_threadsafe(
                                self.stream.check_stream(), self.loop
                            )
                            is_active = check_future.result(timeout=5.0)  # 增加超时时间
                            if not is_active:
                                self.logger.error(f"流不再活跃，停止处理: {self.stream_key}")
                                asyncio.run_coroutine_threadsafe(self.stop(), self.loop)
                                break
                        except asyncio.TimeoutError:
                            self.logger.warning(f"检查流状态超时: {self.stream_key}")
                            # 超时不认为是失败，继续处理
                        except Exception as e:
                            self.logger.error(f"检查流状态时出错: {self.stream_key}, 错误: {str(e)}")

                    # 使用更高的空帧阈值
                    if empty_frame_count >= self.empty_frame_threshold:
                        self.logger.error(f"连续获取空帧次数超过{self.empty_frame_threshold}，停止处理: {self.stream_key}")
                        asyncio.run_coroutine_threadsafe(self.stop(), self.loop)
                        break
                    time.sleep(0.05)  # 减少空帧时的等待时间
                    continue
                
                if frame is None:
                    read_fail_count += 1
                    if read_fail_count >= self.read_fail_threshold:
                        self.logger.error(f"连续获取空帧次数超过{self.read_fail_threshold}，停止处理: {self.stream_key}")
                        asyncio.run_coroutine_threadsafe(self.stop(), self.loop)
                        break
                    time.sleep(0.1)
                    continue

                # 重置失败计数
                read_fail_count = 0
                empty_frame_count = 0  # 重置空帧计数
                current_time = time.time()
                self.last_successful_read = current_time
                timestamp = int(current_time * 1000)  # 转换为毫秒

                # 添加帧到缓存
                try:
                    self.frame_buffer.add_frame(frame, timestamp)
                except Exception as e:
                    self.logger.error(f"添加帧到缓存失败: {self.stream_key}, 错误: {str(e)}")
                    # 继续处理，不因为缓存问题停止
                
                # 每100帧记录一次缓存状态
                if self.frame_buffer.frame_count % 100 == 0:
                    self.logger.debug(f"帧缓存状态: 总帧数={self.frame_buffer.frame_count}, 缓存帧数={len(self.frame_buffer.frames)}")

                # 按照检测间隔进行目标检测
                if current_time - last_detection_time >= self.detection_interval:
                    self.logger.debug(f"触发检测逻辑: {self.stream_key}, 间隔={current_time - last_detection_time:.2f}s, 配置间隔={self.detection_interval}s")
                    try:
                        # 获取所有目标配置
                        target_configs = [
                            next((t for t in config.detection.targets if t['id'] == int(target_id)), None)
                            for target_id in config.detection.target_ids
                        ]
                        target_configs = [t for t in target_configs if t is not None]
                        self.logger.debug(f"获取到目标配置: {len(target_configs)}个目标, target_ids={config.detection.target_ids}")

                        if not target_configs:
                            self.logger.warning(f"没有有效的目标配置，跳过检测: {self.stream_key}")
                        else:
                            # 如果熔断打开则跳过
                            now = time.time()
                            if now < self._circuit_open_until:
                                self.logger.debug("检测熔断已打开，跳过本次检测")
                            else:
                                self.logger.debug(f"提交检测任务: {self.stream_key}, 目标数量={len(target_configs)}")
                                # 提交受限检测任务（包含并发Semaphore与超时控制）
                                asyncio.run_coroutine_threadsafe(
                                    self._run_detection_with_limits(frame.copy(), target_configs, timestamp),
                                    self.loop
                                )

                    except Exception as e:
                        self.logger.error(f"创建目标检测任务时出错: {self.stream_key}, 错误: {str(e)}")
                        import traceback
                        self.logger.error(f"检测任务创建错误详情: {traceback.format_exc()}")

                    # 更新最后检测时间
                    last_detection_time = current_time
                else:
                    # 添加调试日志，每30秒输出一次等待信息
                    if int(current_time) % 30 == 0 and int(current_time) != getattr(self, '_last_debug_time', 0):
                        self.logger.debug(f"等待检测间隔: {self.stream_key}, 剩余时间={self.detection_interval - (current_time - last_detection_time):.2f}s")
                        self._last_debug_time = int(current_time)
                    
                # 控制处理速度
                time.sleep(1.0 / self.frame_rate)
                    
            except Exception as e:
                self.logger.error(f"处理流时出错: {self.stream_key}, 错误: {str(e)}")
                import traceback
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                # 短暂等待后继续尝试
                time.sleep(1)
        
        self.logger.info(f"流处理循环结束: {self.stream_key}")

    async def _handle_detection_result(self, task, timestamp):
        """
        处理检测任务的结果（兼容旧回调风格）
        :param task: 检测任务
        :param timestamp: 检测时间戳
        """
        try:
            result = task.result()
            if result:
                result_with_metadata = {
                    "timestamp": timestamp,
                    "drone_code": self.drone_code,
                    "stream_type": self.stream_type,
                    "result": result
                }
                if self.on_detection:
                    await self._call_detection_callback(result_with_metadata)
        except asyncio.CancelledError:
            self.logger.warning(f"检测任务被取消: {self.stream_key}")
        except Exception as e:
            self.logger.error(f"处理检测结果时出错: {self.stream_key}, 错误: {str(e)}")
            import traceback
            self.logger.error(f"处理检测结果错误详情: {traceback.format_exc()}")

    async def _run_detection_with_limits(self, frame: Any, target_configs: List[Dict], timestamp: float):
        """在事件循环中执行受限检测：并发Semaphore + 超时 + 熔断统计。"""
        # 进入检测协程的第一时间打印调试信息，确认调度成功
        try:
            running_loop = asyncio.get_running_loop()
            frame_shape = getattr(frame, 'shape', None)
            self.logger.debug(
                f"进入检测协程: {self.stream_key}, 目标数={len(target_configs)}, ts={timestamp}, "
                f"frame_shape={frame_shape}, loop_id={id(running_loop)}, configured_loop_id={id(self.loop)}"
            )
        except RuntimeError:
            self.logger.warning(f"进入检测协程但无运行中的事件循环: {self.stream_key}")

        # 惰性初始化信号量
        if self._detection_semaphore is None:
            from app.utils.config import config as _cfg
            max_conc = getattr(_cfg, 'DETECTION_MAX_CONCURRENCY', 2)
            self._detection_semaphore = asyncio.Semaphore(max_conc)

        # 尝试立即获取信号量，无法获取则跳过（背压）
        try:
            await asyncio.wait_for(self._detection_semaphore.acquire(), timeout=0.001)
        except asyncio.TimeoutError:
            self.logger.debug("检测并发已满，跳过本次检测")
            return

        try:
            from app.utils.config import config as _cfg
            timeout_sec = getattr(_cfg, 'DETECTION_TIMEOUT', 5.0)
            breaker_threshold = getattr(_cfg, 'DETECTION_CIRCUIT_BREAKER_THRESHOLD', 5)
            breaker_cooldown = getattr(_cfg, 'DETECTION_CIRCUIT_BREAKER_COOLDOWN', 10.0)

            try:
                self.logger.info(
                    f"开始调用 detect_targets: {self.stream_key}, 超时={timeout_sec}s, 目标数={len(target_configs)}"
                )
                result = await asyncio.wait_for(
                    self.image_processor.detect_targets(frame, target_configs, self.drone_code),
                    timeout=timeout_sec
                )
                self.logger.info(
                    f"detect_targets 调用完成: {self.stream_key}, 有结果={bool(result)}, has_dets={bool(result and result.get('detections'))}"
                )
                # 成功，重置失败计数
                self._detection_failures = 0
                if result:
                    result_with_metadata = {
                        "timestamp": timestamp,
                        "drone_code": self.drone_code,
                        "stream_type": self.stream_type,
                        "result": result
                    }
                    if self.on_detection:
                        await self._call_detection_callback(result_with_metadata)
            except asyncio.TimeoutError:
                self._detection_failures += 1
                self.logger.warning("检测任务超时")
            except Exception as e:
                self._detection_failures += 1
                self.logger.error(f"检测任务失败: {e}")

            # 熔断判断
            if self._detection_failures >= breaker_threshold:
                self._circuit_open_until = time.time() + breaker_cooldown
                self.logger.warning(
                    f"连续检测失败 {self._detection_failures} 次，开启熔断 {breaker_cooldown}s")
        finally:
            self._detection_semaphore.release()

    async def _call_detection_callback(self, result: Dict):
        """调用检测回调函数"""
        try:
            # 调用回调函数
            if self.on_detection:
                self.logger.info(f"准备上报事件: {self.stream_key}")
                await self.on_detection(result, self)
        except Exception as e:
            self.logger.error(f"调用检测回调函数失败: {str(e)}")
            import traceback
            self.logger.error(f"上报事件异常详情: {traceback.format_exc()}")

class StreamMonitor:
    """流监控器 - 管理多个视频流处理器"""
    
    def __init__(self, video_manager: VideoStreamManager):
        """
        初始化监控器
        :param video_manager: 视频流管理器实例
        """
        self.video_manager = video_manager
        self.check_interval = config.stream_monitor.check_interval
        self.is_running = False
        self._monitor_task = None
        # 存储所有活跃的处理器，键为 "{drone_code}_{stream_type}"
        self.processors: Dict[str, StreamProcessor] = {}
        
    async def start(self):
        """启动监控"""
        if self.is_running:
            logger.warning("监控器已经在运行")
            return
            
        self.is_running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("流监控器已启动")
        
    async def stop(self):
        """停止监控"""
        if not self.is_running:
            return
            
        self.is_running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
                
        # 停止所有处理器
        stop_tasks = []
        for processor in self.processors.values():
            stop_tasks.append(processor.stop())
        if stop_tasks:
            await asyncio.gather(*stop_tasks)
            
        self.processors.clear()
        await self.video_manager.cleanup()
        logger.info("流监控器已停止")
        
    async def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 定期输出心跳日志
                current_time = time.time()
                if current_time - getattr(self, '_last_heartbeat', 0) > 180:  # 3分钟输出一次心跳
                    # 增加内存监控
                    try:
                        import psutil
                        import os
                        process = psutil.Process(os.getpid())
                        memory_mb = process.memory_info().rss / 1024 / 1024

                        # 统计处理器状态
                        active_processors = sum(1 for p in self.processors.values() if hasattr(p, 'is_running') and p.is_running)

                        logger.info(f"[内存监控] 流监控器心跳检查 - 运行状态: {self.is_running}, "
                                  f"处理器数: {len(self.processors)} (活跃: {active_processors}), "
                                  f"内存使用: {memory_mb:.1f}MB")
                    except Exception as e:
                        logger.warning(f"[内存监控] 获取流监控器资源信息失败: {e}")
                        logger.info(f"流监控器心跳检查 - 运行状态: {self.is_running}, 处理器数: {len(self.processors)}")

                    self._last_heartbeat = current_time
                
                # 使用统一的共享状态管理器获取活跃无人机列表
                from app.utils.shared_state import shared_state_manager
                active_drone_ids = shared_state_manager.get_active_drone_ids()

                logger.debug(f"流监控器获取到活跃无人机列表: {active_drone_ids}")

                # 处理每个活跃的无人机
                for drone_code in active_drone_ids:
                    try:
                        # 为每个无人机的处理添加超时控制
                        await asyncio.wait_for(self._manage_drone_streams(drone_code), timeout=30.0)
                    except asyncio.TimeoutError:
                        logger.warning(f"管理无人机 {drone_code} 的视频流超时")
                    except Exception as e:
                        logger.error(f"管理无人机 {drone_code} 的视频流失败: {str(e)}")

                # 清理不再活跃的处理器
                try:
                    await asyncio.wait_for(self._cleanup_inactive_processors(active_drone_ids), timeout=30.0)
                except asyncio.TimeoutError:
                    logger.warning("清理非活跃处理器超时")
                except Exception as e:
                    logger.error(f"清理非活跃处理器失败: {str(e)}")
                    
            except Exception as e:
                logger.error(f"监控循环发生错误: {str(e)}")
                import traceback
                logger.error(f"错误详情: {traceback.format_exc()}")
                
            await asyncio.sleep(self.check_interval)
            
    async def _manage_drone_streams(self, drone_code: str):
        """
        管理单个无人机的视频流
        :param drone_code: 无人机编号
        """
        # 处理无人机视频流
        await self._manage_stream(drone_code, "drone")
        
        # 处理机场视频流（如果配置了）
        if config.video_stream.process_airport_stream:
            await self._manage_stream(drone_code, "airport")
            
    async def _manage_stream(self, drone_code: str, stream_type: str):
        """
        管理特定无人机的特定类型视频流
        :param drone_code: 无人机编号
        :param stream_type: 视频流类型
        """
        # 构建流标识符
        stream_key = f"{drone_code}_{stream_type}"
        logger.debug(f"管理视频流: {stream_key}")

        try:
            # 获取视频流
            stream = await self.video_manager.get_stream(drone_code, stream_type)
            logger.info(f"获取视频流结果: {stream_key}, stream对象: {stream is not None}")

            # 如果无法获取流，记录日志并返回
            if not stream:
                logger.warning(f"无法获取视频流: {stream_key}")
                if stream_key in self.processors:
                    logger.info(f"视频流不可用，停止处理器: {stream_key}")
                    await self.processors[stream_key].stop()
                    del self.processors[stream_key]
                return

            # 检查处理器状态
            processor_exists = stream_key in self.processors
            processor_running = processor_exists and self.processors[stream_key].is_running
            logger.info(f"处理器状态检查: {stream_key}, 存在={processor_exists}, 运行中={processor_running}")

            # 如果处理器不存在或不在运行，创建并启动新的处理器
            if not processor_exists or not processor_running:
                logger.info(f"创建新的流处理器: {stream_key}")
                processor = StreamProcessor(
                    drone_code=drone_code,
                    stream_id=stream_key,
                    stream_type=stream_type,
                    on_detection=self._on_detection
                )

                # 启动处理器
                logger.info(f"尝试启动流处理器: {stream_key}")
                if await processor.start(stream):
                    self.processors[stream_key] = processor
                    logger.info(f"成功启动流处理器: {stream_key}")
                else:
                    logger.error(f"启动流处理器失败: {stream_key}")
            else:
                logger.debug(f"流处理器已存在且运行中: {stream_key}")

        except Exception as e:
            logger.error(f"管理视频流失败: {stream_key}, 错误: {str(e)}")
            import traceback
            logger.error(f"管理视频流错误详情: {traceback.format_exc()}")
            
    async def _cleanup_inactive_processors(self, active_drone_ids: List[str]):
        """
        清理不再活跃的处理器
        :param active_drone_ids: 活跃的无人机列表
        """
        # 找出不再活跃的处理器
        inactive_keys = []
        for stream_key, processor in self.processors.items():
            # 检查处理器对应的无人机是否仍然活跃
            drone_code = processor.drone_code
            if drone_code not in active_drone_ids:
                logger.info(f"无人机 {drone_code} 不再活跃，标记处理器 {stream_key} 为不活跃")
                inactive_keys.append(stream_key)
                continue
                
            # 检查处理器是否长时间未收到帧
            if processor.last_successful_read > 0:
                inactive_time = time.time() - processor.last_successful_read
                if inactive_time > config.stream_monitor.check_interval * 2:
                    logger.warning(f"处理器长时间未收到帧: {stream_key}, {inactive_time:.1f}秒")
                    inactive_keys.append(stream_key)
        
        # 停止不再活跃的处理器
        for stream_key in inactive_keys:
            if stream_key in self.processors:
                logger.info(f"停止不再活跃的处理器: {stream_key}")
                await self.processors[stream_key].stop()
                del self.processors[stream_key]
                
    async def _on_detection(self, result: Dict, processor: StreamProcessor):
        """
        检测到目标时的回调函数
        :param result: 检测结果
        :param processor: 处理器实例
        """
        try:
            logger.info(f"检测到目标: {processor.stream_key}, 结果类型: {type(result['result'])}, 内容: {result['result'].get('detections')}")
            
            # 保存视频片段
            timestamp = result["timestamp"]
            logger.info(f"准备保存检测视频，时间戳: {timestamp}")
            
            try:
                # 使用FileManager的save_detection_video方法保存视频
                video_path = await file_manager.save_detection_video(processor, timestamp)
                
                if not video_path:
                    logger.error(f"保存视频失败: {processor.stream_key}, 时间戳: {timestamp}")
                else:
                    # 添加视频路径到结果中
                    result["video_path"] = video_path
                    logger.info(f"成功保存视频: {video_path}, 文件大小: {os.path.getsize(video_path) if os.path.exists(video_path) else '文件不存在'} 字节")
            except Exception as e:
                logger.error(f"保存视频过程中发生异常: {str(e)}")
                import traceback
                logger.error(f"保存视频异常详情: {traceback.format_exc()}")
            
            # 上报事件
            try:
                logger.info(f"准备上报事件: {processor.stream_key}")
                from app.utils.event_reporter import event_reporter
                report_result = await event_reporter.report_event(result, processor)
                if report_result:
                    logger.info(f"成功上报事件: {processor.stream_key}")
                else:
                    logger.warning(f"事件上报可能未成功: {processor.stream_key}")
            except Exception as e:
                logger.error(f"上报事件失败: {str(e)}")
                import traceback
                logger.error(f"上报事件异常详情: {traceback.format_exc()}")
            
        except Exception as e:
            logger.error(f"处理检测结果失败: {str(e)}")
            import traceback
            logger.error(f"处理检测结果异常详情: {traceback.format_exc()}")
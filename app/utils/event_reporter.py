import logging
import aiohttp
from aiohttp import TCPConnector, ClientError, ClientResponseError, ServerDisconnectedError
import os
from typing import Optional
from app.utils.config import config
from app.utils.file_manager import file_manager
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)

class EventReporter:
    def __init__(self):
        """初始化事件上报器"""
        self.event_api_url = config.EVENT_REPORT_URL  # getDroneEventData接口
        self.video_api_url = config.VIDEO_SAVE_URL    # receiveVideo接口
        self.camera_type = config.EVENT_REPORT_CAMERA_TYPE
        self.identify_type = config.EVENT_REPORT_IDENTIFY_TYPE

        # 设置HTTP请求超时配置
        self.timeout = aiohttp.ClientTimeout(
            total=config.HTTP_TIMEOUT_TOTAL,      # 总超时时间
            connect=config.HTTP_TIMEOUT_CONNECT,  # 连接超时
            sock_read=config.HTTP_TIMEOUT_SOCK_READ  # 读取超时
        )

        # 轻量速率限制（每个请求之间的最小间隔，秒），可通过配置覆盖；默认0.1秒，缓解并发抖动
        self._min_interval = getattr(config, 'HTTP_MIN_INTERVAL', 0.1)
        # 轻量限流与会话控制：按loop维度管理锁与时间戳，避免跨loop等待
        self._last_request_ts_map: dict[int, float] = {}
        self._interval_locks: dict[int, asyncio.Lock] = {}
        # 会话创建/重置的互斥锁（按loop维度）
        self._session_locks: dict[int, asyncio.Lock] = {}

        # 按事件循环(loop)维度复用HTTP会话，避免跨loop复用导致的不稳定（如 Connection closed）
        # key: id(asyncio.get_running_loop()) -> ClientSession
        self._sessions: dict[int, aiohttp.ClientSession] = {}

        # 初始化文件与依赖
        self.file_manager = file_manager
        from app.database.drone_info import DroneInfoManager
        self.drone_manager = DroneInfoManager()

        # 记录API URL信息
        logger.info(f"事件上报URL: {self.event_api_url}")
        logger.info(f"视频保存URL: {self.video_api_url}")
        logger.info(f"HTTP请求超时配置: 总超时={self.timeout.total}s, 连接超时={self.timeout.connect}s, 读取超时={self.timeout.sock_read}s")
        logger.info(f"HTTP最小请求间隔: {self._min_interval}s（可通过配置 HTTP_MIN_INTERVAL 覆盖）")

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建按事件循环(loop)维度复用的HTTP会话；禁用keep-alive减少连接复用问题"""
        loop = asyncio.get_running_loop()
        key = id(loop)
        # 为每个loop维护独立的锁，避免跨loop互斥
        lock = self._session_locks.get(key)
        if lock is None:
            lock = asyncio.Lock()
            self._session_locks[key] = lock
        async with lock:
            session = self._sessions.get(key)
            if session is None or session.closed:
                # 使用默认连接管理（keep-alive），避免强制关闭带来的握手异常
                session = aiohttp.ClientSession(timeout=self.timeout)
                self._sessions[key] = session
                logger.debug(f"创建新的HTTP会话: loop_id={key}, keep_alive=True")
            return session

    async def _sleep_min_interval(self):
        """如配置了最小请求间隔，则按需等待（轻量限流，按loop维度）。"""
        if self._min_interval <= 0:
            return
        loop = asyncio.get_running_loop()
        key = id(loop)
        lock = self._interval_locks.get(key)
        if lock is None:
            lock = asyncio.Lock()
            self._interval_locks[key] = lock
        async with lock:
            now = loop.time()
            last = self._last_request_ts_map.get(key, 0.0)
            delta = now - last
            if delta < self._min_interval:
                await asyncio.sleep(self._min_interval - delta)
            self._last_request_ts_map[key] = loop.time()

    async def _reset_session(self):
        """重置当前事件循环(loop)对应的HTTP会话：删除会话引用，后续按需重建。"""
        loop = asyncio.get_running_loop()
        key = id(loop)
        lock = self._session_locks.get(key)
        if lock is None:
            lock = asyncio.Lock()
            self._session_locks[key] = lock
        async with lock:
            if key in self._sessions:
                # 不强制关闭旧会话，避免干扰并发中的请求；让其自然回收
                del self._sessions[key]

    def _build_form_data(self, payload: dict) -> aiohttp.FormData:
        """将纯键值对构造成 multipart/form-data（服务器要求为 form-data）。"""
        form = aiohttp.FormData()
        for k, v in payload.items():
            form.add_field(k, "" if v is None else str(v))
        return form



    async def _post_cm_or_await(self, session: aiohttp.ClientSession, url: str, data: dict):
        """
        发送HTTP POST请求并返回响应
        """
        # 直接使用标准的aiohttp方式
        async with session.post(url, data=data, timeout=self.timeout) as response:
            # 在上下文管理器内读取响应内容
            response_text = await response.text()

            # 创建一个简单的响应对象，包含我们需要的信息
            class SimpleResponse:
                def __init__(self, status, text, request_info=None):
                    self.status = status
                    self._text = text
                    self.request_info = request_info

                async def text(self):
                    return self._text

            return SimpleResponse(response.status, response_text, response.request_info)

    async def _with_retry(self, func, retries: int = 2, base_delay: float = 0.2, op_name: str = "HTTP"):
        """对可重试错误进行轻量重试（指数退避），返回True/False。仅在失败且重试耗尽或出现4xx时输出一次告警，避免高频日志。"""
        attempt = 0
        last_err: Exception | None = None
        while True:
            try:
                logger.debug(f"[{op_name}] 重试尝试: attempt={attempt+1}/{retries+1}")
                await func()
                return True
            except ClientResponseError as e:
                last_err = e
                # 4xx 不重试，直接返回并记录一次告警
                if 400 <= e.status < 500:
                    logger.warning(f"[{op_name}] 请求返回客户端错误: status={e.status}, message={e.message}")
                    return False
            except (ServerDisconnectedError, ClientError, asyncio.TimeoutError, RuntimeError) as e:
                last_err = e
                # 针对连接类异常（包含“Connection closed”“Session is closed”），重置会话后再重试
                msg = str(e)
                if (
                    isinstance(e, ServerDisconnectedError)
                    or "Connection closed" in msg
                    or "Session is closed" in msg
                ):
                    logger.debug(f"[{op_name}] 触发会话重置: {msg}")
                    await self._reset_session()
            attempt += 1
            if attempt > retries:
                if last_err is not None:
                    logger.warning(f"[{op_name}] 请求重试耗尽: {type(last_err).__name__}: {last_err}")
                else:
                    logger.warning(f"[{op_name}] 请求重试耗尽")
                return False
            await asyncio.sleep(base_delay * (2 ** (attempt - 1)))

    async def report_event(self, result: dict, stream_processor) -> bool:
        """
        上报检测事件
        :param result: 检测结果
        :param stream_processor: StreamProcessor实例
        :return: 上报是否成功
        """
        try:
            drone_code = result["drone_code"]
            timestamp = result["timestamp"]
            detection_result = result["result"]
            video_path = result.get("video_path", "")  # 从result中获取视频路径

            # 检查结果是否有效
            if not detection_result or not isinstance(detection_result, dict):
                logger.warning(f"检测结果无效或格式不正确: {type(detection_result)}")
                return False

            # 获取无人机信息（缺失时不再提前返回，使用回退逻辑继续流程，避免阻断视频上报）
            drone_info = self.drone_manager.get_drone_info(drone_code)
            if not drone_info:
                logger.error(f"未找到无人机 {drone_code} 的信息")
            else:
                # 记录无人机位置信息，用于调试
                # logger.debug(f"无人机 {drone_code} 位置信息: 经度={drone_info.longitude}, 纬度={drone_info.latitude}")

                # 由于使用了单例模式，这里的drone_info应该已经是最新的
                # 但为了确保，我们再次检查位置信息是否有效
                if drone_info.longitude == 0.0 or drone_info.latitude == 0.0:
                    logger.warning(f"无人机 {drone_code} 位置信息无效: 经度={getattr(drone_info, 'longitude', 0.0)}, 纬度={getattr(drone_info, 'latitude', 0.0)}")
                    # 尝试从共享状态中获取位置信息
                    try:
                        from app.utils.shared_state import active_drones
                        if drone_code in active_drones:
                            logger.info(f"尝试从共享状态获取无人机位置信息: {active_drones[drone_code]}")
                    except Exception as e:
                        logger.warning(f"尝试从共享状态获取位置信息失败: {str(e)}")

            # 如果未配置事件上报URL，则跳过上报
            if not self.event_api_url:
                logger.warning("事件上报URL未配置，跳过事件上报")
                return False

            # 对每个检测到的目标分别上报事件
            detections = detection_result.get("detections", [])
            if not detections:
                logger.warning(f"未检测到目标: {drone_code}")
                return False

            logger.info(f"检测到 {len(detections)} 个目标为{detections}，准备上报")

            # 收集所有检测到的目标名称，用于设置business_type
            target_names = []
            for detection in detections:
                target_name = detection.get("target_name")
                if target_name and target_name not in target_names:
                    target_names.append(target_name)

            # 将所有目标名称用逗号连接
            business_type = "，".join(target_names) if target_names else "船只"
            logger.info(f"设置business_type为: {business_type}")

            # 跟踪是否至少有一个目标成功上报
            any_success = False

            for detection in detections:
                # 获取图片路径
                init_image_path = detection_result.get("image_paths", {}).get("init", "")
                res_image_path = detection_result.get("image_paths", {}).get("res", "")

                # 只使用文件名而不是完整路径
                init_image_filename = os.path.basename(init_image_path) if init_image_path else ""
                res_image_filename = os.path.basename(res_image_path) if res_image_path else ""
                video_filename = os.path.basename(video_path) if video_path else ""

                if drone_info:
                    logger.debug(f"检查无人机{drone_info.drone_code} 位置信息: 经度={drone_info.longitude}, 纬度={drone_info.latitude}")

                sn_to_report = drone_info.drone_code if drone_info else drone_code

                # 构建事件数据 (getDroneEventData接口)
                event_data = {
                    "imageinit": init_image_filename,  # 初始图片文件名
                    "imageres": res_image_filename,  # 标注后图片文件名
                    "timestamp": str(int(timestamp)),  # 时间戳
                    "err": detection.get("error_code", "2"),  # 错误码
                    "camera": "fly_camera",  # 相机类型
                    "snNum": sn_to_report,  # 无人机编码
                    "business_type": business_type,  # 业务类型，使用所有检测到的目标名称
                    "identifyType": str(self.identify_type),  # 识别类型
                }

                # 尝试从不同来源获取经纬度
                longitude = 0.0
                latitude = 0.0

                # 1. 首先尝试从drone_info对象获取
                if drone_info and drone_info.longitude != 0.0 and drone_info.latitude != 0.0:
                    longitude = drone_info.longitude
                    latitude = drone_info.latitude
                    logger.debug(f"使用drone_info中的位置: 经度={longitude}, 纬度={latitude}")
                # 2. 如果drone_info中没有，尝试从共享状态获取
                else:
                    try:
                        from app.utils.shared_state import active_drones
                        if drone_code in active_drones and 'longitude' in active_drones[drone_code] and 'latitude' in active_drones[drone_code]:
                            longitude = active_drones[drone_code]['longitude']
                            latitude = active_drones[drone_code]['latitude']
                            logger.info(f"使用共享状态中的位置: 经度={longitude}, 纬度={latitude}")
                    except Exception as e:
                        logger.warning(f"从共享状态获取位置失败: {str(e)}")

                # 添加经纬度到事件数据
                event_data["longitude"] = str(longitude)
                event_data["latitude"] = str(latitude)

                # 记录最终上报的经纬度
                logger.debug(f"最终上报的经纬度: 经度={event_data['longitude']}, 纬度={event_data['latitude']}")

                # 调用getDroneEventData接口（复用会话 + 轻量重试 + 最小间隔）
                try:
                    await self._sleep_min_interval()
                    logger.info(f"正在调用事件上报接口: {self.event_api_url}")
                    logger.debug(f"上报事件数据: {event_data}")

                    async def _post_once():
                        # 每次重试都重新获取会话，配合 _reset_session 生效
                        session = await self._get_session()
                        response = await self._post_cm_or_await(session, self.event_api_url, self._build_form_data(event_data))
                        response_text = await response.text()
                        # 接受 2xx 为成功
                        if not (200 <= response.status < 300):
                            raise aiohttp.ClientResponseError(request_info=response.request_info, history=(), status=response.status, message=response_text)
                        return response_text

                    event_success = await self._with_retry(_post_once)
                    if event_success:
                        logger.info(f"成功上报事件: {drone_code}, 目标类型: {detection.get('target_category', '未知')}")
                        any_success = True
                    else:
                        logger.warning(f"事件上报重试后仍失败: {drone_code}, 目标类型: {detection.get('target_category', '未知')}")
                except Exception as e:
                    logger.error(f"调用事件上报接口失败: {str(e)}")
                    logger.error(f"请求URL: {self.event_api_url}")
                    import traceback
                    logger.error(f"调用事件上报接口异常详情: {traceback.format_exc()}")
                    # 单个目标上报失败，继续处理下一个目标
                    event_success = False

                # 如果当前目标上报失败，继续处理下一个目标
                if not event_success:
                    continue

            # 如未携带视频路径，则在此处保存视频片段（兼容单元测试期望）
            if not video_path:
                try:
                    video_path = await self.save_detection_video(stream_processor, timestamp)
                    if video_path:
                        result["video_path"] = video_path
                except Exception as e:
                    logger.warning(f"保存检测视频失败（非致命）: {str(e)}")

            # 调用receiveVideo接口上报视频信息
            video_success = False
            if self.video_api_url and video_path:
                # 只使用文件名而不是完整路径
                video_filename = os.path.basename(video_path)
                logger.info(f"准备上报视频信息: {video_filename}, 完整路径: {video_path}")
                try:
                    video_success = await self._report_video_info(video_filename, sn_to_report, timestamp)
                except Exception:
                    video_success = False
            else:
                if not self.video_api_url:
                    logger.warning("视频上传URL未配置，跳过视频上传")
                else:
                    logger.warning("视频文件路径为空，跳过视频上传")

            # 返回综合结果：任一成功即认为本次上报成功，避免误报警告
            return any_success or video_success

        except Exception as e:
            logger.error(f"上报事件失败: {str(e)}")
            import traceback
            logger.error(f"上报事件异常详情: {traceback.format_exc()}")
            return False

    async def save_detection_video(self, stream_processor, timestamp: float, duration: float = 5.0) -> Optional[str]:
        """
        保存检测到目标时的视频片段（EventReporter 轻量封装，便于单测与回溯）
        :param stream_processor: StreamProcessor实例
        :param timestamp: 检测时间戳（毫秒）
        :param duration: 前后抓取的总时长（秒）
        :return: 保存的视频文件路径
        """
        try:
            # 从时间戳附近抓取帧
            frames_info = stream_processor.get_frames_around_timestamp(timestamp, duration)
            if not frames_info:
                logger.warning(f"未找到时间戳 {timestamp} 附近的帧")
                return None

            # 计算帧尺寸与帧列表
            height, width = frames_info[0].frame.shape[:2]
            frames = [fi.frame for fi in frames_info]

            # 生成简单文件名（测试只校验关键参数，不依赖文件名）
            filename = f"{stream_processor.drone_code}_{int(timestamp)}.mp4"

            # 调用底层保存
            return await self.file_manager.save_video(
                frames=frames,
                filename=filename,
                frame_size=(width, height),
                frame_rate=stream_processor.frame_rate,
            )
        except Exception as e:
            logger.error(f"保存检测视频失败: {str(e)}")
            return None


        except Exception as e:
            logger.error(f"上报事件失败: {str(e)}")
            import traceback
            logger.error(f"上报事件异常详情: {traceback.format_exc()}")
            return False

    async def _report_video_info(self, video_filename: str, sn_num: str, timestamp: float) -> bool:
        """
        调用receiveVideo接口上报视频信息（只上报视频名称，不上传视频文件）
        :param video_filename: 视频文件名（不是完整路径）
        :param sn_num: 无人机编号
        :param timestamp: 时间戳
        :return: 是否上报成功
        """
        try:
            # 检查视频文件是否存在（仅用于日志提示，不阻断）
            if not os.path.exists(video_filename):
                date_str = datetime.fromtimestamp(timestamp / 1000.0).strftime('%Y%m%d')
                video_dir = os.path.join(self.file_manager.temp_dirs['videos'], date_str)
                full_path = os.path.join(video_dir, video_filename)
                if not os.path.exists(full_path):
                    abs_video_dir = os.path.abspath(video_dir)
                    abs_path = os.path.join(abs_video_dir, video_filename)
                    if not os.path.exists(abs_path):
                        logger.warning(f"视频文件不存在: {video_filename}")
                        logger.warning(f"尝试的路径: 当前目录, {full_path}, {abs_path}")

            # 构建上传数据 (receiveVideo接口)
            data = {
                "video": video_filename,
                "snNum": sn_num,
                "timestamp": str(int(timestamp)),
                "identifyType": str(self.identify_type)
            }

            await self._sleep_min_interval()

            async def _post_once():
                # 每次重试重新获取会话，配合 _reset_session 生效
                session = await self._get_session()
                response = await self._post_cm_or_await(session, self.video_api_url, self._build_form_data(data))
                response_text = await response.text()
                if not (200 <= response.status < 300):
                    raise aiohttp.ClientResponseError(request_info=response.request_info, history=(), status=response.status, message=response_text)
                return response_text

            try:
                ok = await self._with_retry(_post_once)
                if ok:
                    logger.info(f"成功上报视频信息: {sn_num}, 视频文件名: {video_filename}")
                else:
                    logger.error(f"上报视频信息失败: 重试耗尽或客户端错误")
                return ok
            except Exception as e:
                logger.error(f"上报视频信息失败: {str(e)}")
                logger.error(f"请求URL: {self.video_api_url}")
                logger.error(f"请求数据: {data}")
                return False

        except Exception as e:
            logger.error(f"上报视频信息失败: {str(e)}")
            import traceback
            logger.error(f"上报视频信息异常详情: {traceback.format_exc()}")
            return False

# 创建全局实例
event_reporter = EventReporter()